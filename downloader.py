import sys
import os
import subprocess
import uuid
import json
import re

def download_spotify_url(spotify_url: str, quality: str = "320k"):
    base_output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'downloads')
    request_id = str(uuid.uuid4())
    output_path = os.path.join(base_output_path, request_id)
    os.makedirs(output_path, exist_ok=True)

    # Cross-platform spotdl detection
    if os.name == 'nt':  # Windows
        python_dir = os.path.dirname(sys.executable)
        spotdl_path = os.path.join(python_dir, 'Scripts', 'spotdl.exe')
        if not os.path.exists(spotdl_path):
            spotdl_path = 'spotdl'
    else:  # Linux/macOS/Ubuntu
        spotdl_path = 'spotdl'

    cleaned_url = spotify_url.split('?')[0]

    # Quality mapping - spotdl expects the 'k' suffix
    quality_map = {
        "128k": "128k",
        "192k": "192k",
        "256k": "256k",
        "320k": "320k"
    }

    bitrate = quality_map.get(quality, "320k")

    command = [
        spotdl_path,
        "--output",
        output_path,
        "--bitrate",
        bitrate,
        cleaned_url
    ]

    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True,
            encoding='utf-8'
        )

        output_lines = result.stdout.strip().splitlines()
        downloaded_files = []
        for line in output_lines:
            if "Downloaded" in line:
                try:
                    downloaded_file = line.split('" to "')[1].strip().replace('"', '')
                    downloaded_files.append(downloaded_file)
                except IndexError:
                    continue
        
        if downloaded_files:
            print(json.dumps({"files": downloaded_files, "temp_dir": output_path}))
        else:
            list_of_files = [os.path.join(output_path, f) for f in os.listdir(output_path)]
            if not list_of_files:
                print(json.dumps({"files": [], "temp_dir": output_path}))
                sys.exit(0)

            print(json.dumps({"files": list_of_files, "temp_dir": output_path}))


    except subprocess.CalledProcessError as e:
        print(json.dumps({"files": [], "temp_dir": output_path}))
        print(f"An error occurred with spotdl: {e}", file=sys.stderr)
        print(f"spotdl stderr: {e.stderr}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(json.dumps({"files": [], "temp_dir": output_path}))
        print(f"An unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)

def download_youtube_url(youtube_url: str):
    base_output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'downloads')
    request_id = str(uuid.uuid4())
    output_path = os.path.join(base_output_path, request_id)
    os.makedirs(output_path, exist_ok=True)

    # Use yt-dlp for YouTube downloads
    command = [
        'yt-dlp',
        '--extract-flat',
        '--no-playlist' if 'playlist' not in youtube_url else '',
        '--format', 'bestaudio[ext=m4a]/bestaudio/best',
        '--output', os.path.join(output_path, '%(title)s.%(ext)s'),
        '--embed-metadata',
        '--add-metadata',
        youtube_url
    ]

    # Remove empty strings from command
    command = [arg for arg in command if arg]

    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True,
            encoding='utf-8'
        )

        # Get downloaded files
        downloaded_files = []
        for file in os.listdir(output_path):
            if file.endswith(('.m4a', '.mp3', '.webm')):
                downloaded_files.append(os.path.join(output_path, file))

        if downloaded_files:
            print(json.dumps({"files": downloaded_files, "temp_dir": output_path}))
        else:
            print(json.dumps({"files": [], "temp_dir": output_path}))

    except subprocess.CalledProcessError as e:
        print(json.dumps({"files": [], "temp_dir": output_path}))
        print(f"An error occurred with yt-dlp: {e}", file=sys.stderr)
        print(f"yt-dlp stderr: {e.stderr}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(json.dumps({"files": [], "temp_dir": output_path}))
        print(f"An unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)

def search_and_download_song(song_query: str, quality: str = "320k"):
    base_output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'downloads')
    request_id = str(uuid.uuid4())
    output_path = os.path.join(base_output_path, request_id)
    os.makedirs(output_path, exist_ok=True)

    # Cross-platform spotdl detection
    if os.name == 'nt':  # Windows
        python_dir = os.path.dirname(sys.executable)
        spotdl_path = os.path.join(python_dir, 'Scripts', 'spotdl.exe')
        if not os.path.exists(spotdl_path):
            spotdl_path = 'spotdl'
    else:  # Linux/macOS/Ubuntu
        spotdl_path = 'spotdl'

    # Quality mapping - spotdl expects the 'k' suffix
    quality_map = {
        "128k": "128k",
        "192k": "192k",
        "256k": "256k",
        "320k": "320k"
    }

    bitrate = quality_map.get(quality, "320k")

    command = [
        spotdl_path,
        "--output",
        output_path,
        "--bitrate",
        bitrate,
        f'"{song_query}"'
    ]

    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True,
            encoding='utf-8'
        )

        output_lines = result.stdout.strip().splitlines()
        downloaded_files = []
        for line in output_lines:
            if "Downloaded" in line:
                try:
                    downloaded_file = line.split('" to "')[1].strip().replace('"', '')
                    downloaded_files.append(downloaded_file)
                except IndexError:
                    continue
        
        if downloaded_files:
            print(json.dumps({"files": downloaded_files, "temp_dir": output_path}))
        else:
            list_of_files = [os.path.join(output_path, f) for f in os.listdir(output_path)]
            if not list_of_files:
                print(json.dumps({"files": [], "temp_dir": output_path}))
                sys.exit(0)

            print(json.dumps({"files": list_of_files, "temp_dir": output_path}))


    except subprocess.CalledProcessError as e:
        print(json.dumps({"files": [], "temp_dir": output_path}))
        print(f"An error occurred with spotdl: {e}", file=sys.stderr)
        print(f"spotdl stderr: {e.stderr}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(json.dumps({"files": [], "temp_dir": output_path}))
        print(f"An unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)

def is_youtube_url(url: str) -> bool:
    youtube_patterns = [
        r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=[\w-]+',
        r'(?:https?://)?(?:www\.)?youtube\.com/playlist\?list=[\w-]+',
        r'(?:https?://)?youtu\.be/[\w-]+',
        r'(?:https?://)?(?:www\.)?youtube\.com/c/[\w-]+',
        r'(?:https?://)?(?:www\.)?youtube\.com/channel/[\w-]+',
        r'(?:https?://)?music\.youtube\.com/'
    ]

    for pattern in youtube_patterns:
        if re.match(pattern, url, re.IGNORECASE):
            return True
    return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python downloader.py <spotify_url_or_youtube_url_or_song_query> [quality]", file=sys.stderr)
        sys.exit(1)

    input_text = sys.argv[1]
    quality = sys.argv[2] if len(sys.argv) > 2 else "320k"

    # Check if it's a Spotify URL
    if input_text.startswith('https://open.spotify.com/'):
        download_spotify_url(input_text, quality)
    # Check if it's a YouTube URL
    elif is_youtube_url(input_text):
        download_youtube_url(input_text)  # YouTube quality handled differently
    else:
        # Treat as a search query
        search_and_download_song(input_text, quality)