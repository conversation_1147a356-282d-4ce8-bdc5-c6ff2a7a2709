{"version": 3, "file": "WaitTask.js", "sourceRoot": "", "sources": ["../../../../src/common/WaitTask.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAGH,mEAAiE;AAEjE,2CAAyC;AAezC;;GAEG;AACH,MAAa,QAAQ;IAenB,YACE,KAAoB,EACpB,OAAwB,EACxB,EAAiD,EACjD,GAAG,IAAe;;QAlBpB,kCAAsB;QACtB,qCAAsD;QACtD,oCAAsC;QACtC,iCAA4B;QAE5B,+BAAY;QACZ,iCAAiB;QAEjB,oCAA0B;QAE1B,2BAAU,IAAA,0CAAqB,GAAgB,EAAC;QAEhD,mCAA8B;QAQ5B,uBAAA,IAAI,mBAAU,KAAK,MAAA,CAAC;QACpB,uBAAA,IAAI,sBAAa,MAAA,OAAO,CAAC,QAAQ,mCAAI,IAAI,GAAG,EAAE,MAAA,CAAC;QAC/C,uBAAA,IAAI,qBAAY,OAAO,CAAC,OAAO,MAAA,CAAC;QAChC,uBAAA,IAAI,kBAAS,OAAO,CAAC,IAAI,MAAA,CAAC;QAE1B,QAAQ,OAAO,EAAE,EAAE;YACjB,KAAK,QAAQ;gBACX,uBAAA,IAAI,gBAAO,kBAAkB,EAAE,KAAK,MAAA,CAAC;gBACrC,MAAM;YACR;gBACE,uBAAA,IAAI,gBAAO,EAAE,CAAC,QAAQ,EAAE,MAAA,CAAC;gBACzB,MAAM;SACT;QACD,uBAAA,IAAI,kBAAS,IAAI,MAAA,CAAC;QAElB,uBAAA,IAAI,uBAAO,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,uBAAA,IAAI,qBAAY,UAAU,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,SAAS,CACZ,IAAI,wBAAY,CAAC,mBAAmB,OAAO,CAAC,OAAO,aAAa,CAAC,CAClE,CAAC;YACJ,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,MAAA,CAAC;SACrB;QAED,IAAI,uBAAA,IAAI,0BAAU,CAAC,IAAI,KAAK,CAAC,EAAE;YAC7B,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,uBAAA,IAAI,0BAAU,EAAE;gBACvC,uBAAA,IAAI,uBAAO,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;aAC3C;SACF;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED,IAAI,MAAM;QACR,OAAO,uBAAA,IAAI,wBAAQ,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI;YACF,IAAI,uBAAA,IAAI,0BAAU,CAAC,IAAI,KAAK,CAAC,EAAE;gBAC7B,MAAM,OAAO,GAAG,MAAM,uBAAA,IAAI,uBAAO,CAAC,gBAAgB,EAAE,CAAC;gBACrD,MAAM,OAAO,CAAC,GAAG,CACf,CAAC,GAAG,uBAAA,IAAI,0BAAU,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE;oBACvC,OAAO,MAAM,uBAAA,IAAI,uBAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC/D,CAAC,CAAC,CACH,CAAC;aACH;YAED,QAAQ,uBAAA,IAAI,yBAAS,EAAE;gBACrB,KAAK,KAAK;oBACR,uBAAA,IAAI,oBAAW,MAAM,uBAAA,IAAI,uBAAO,CAAC,cAAc,CAC7C,CAAC,EAAC,SAAS,EAAE,cAAc,EAAC,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,EAAE;wBAC3C,MAAM,GAAG,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;wBAC/B,OAAO,IAAI,SAAS,CAAC,GAAG,EAAE;4BACxB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAe,CAAC;wBACpC,CAAC,CAAC,CAAC;oBACL,CAAC,EACD,MAAM,uBAAA,IAAI,uBAAO,CAAC,aAAa,EAC/B,uBAAA,IAAI,oBAAI,EACR,GAAG,uBAAA,IAAI,sBAAM,CACd,MAAA,CAAC;oBACF,MAAM;gBACR,KAAK,UAAU;oBACb,uBAAA,IAAI,oBAAW,MAAM,uBAAA,IAAI,uBAAO,CAAC,cAAc,CAC7C,CAAC,EAAC,cAAc,EAAE,cAAc,EAAC,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,EAAE;wBACtD,MAAM,GAAG,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;wBAC/B,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE;4BAC7B,OAAO,GAAG,CAAC,GAAG,IAAI,CAAe,CAAC;wBACpC,CAAC,EAAE,IAAI,IAAI,QAAQ,CAAC,CAAC;oBACvB,CAAC,EACD,MAAM,uBAAA,IAAI,uBAAO,CAAC,aAAa,EAC/B,uBAAA,IAAI,sBAAM,EACV,uBAAA,IAAI,oBAAI,EACR,GAAG,uBAAA,IAAI,sBAAM,CACd,MAAA,CAAC;oBACF,MAAM;gBACR;oBACE,uBAAA,IAAI,oBAAW,MAAM,uBAAA,IAAI,uBAAO,CAAC,cAAc,CAC7C,CAAC,EAAC,cAAc,EAAE,cAAc,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,EAAE;wBACpD,MAAM,GAAG,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;wBAC/B,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE;4BAC7B,OAAO,GAAG,CAAC,GAAG,IAAI,CAAe,CAAC;wBACpC,CAAC,EAAE,EAAE,CAAC,CAAC;oBACT,CAAC,EACD,MAAM,uBAAA,IAAI,uBAAO,CAAC,aAAa,EAC/B,uBAAA,IAAI,yBAAS,EACb,uBAAA,IAAI,oBAAI,EACR,GAAG,uBAAA,IAAI,sBAAM,CACd,MAAA,CAAC;oBACF,MAAM;aACT;YAED,MAAM,uBAAA,IAAI,wBAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACnC,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,uBAAA,IAAI,wBAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;gBACxD,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC;YACH,uBAAA,IAAI,wBAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAE7B,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;SACxB;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACzC,IAAI,QAAQ,EAAE;gBACZ,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;aAChC;SACF;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,KAAe;QAC7B,uBAAA,IAAI,uBAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,uBAAA,IAAI,yBAAS,EAAE;YACjB,YAAY,CAAC,uBAAA,IAAI,yBAAS,CAAC,CAAC;SAC7B;QAED,IAAI,KAAK,IAAI,CAAC,uBAAA,IAAI,wBAAQ,CAAC,QAAQ,EAAE,EAAE;YACrC,uBAAA,IAAI,wBAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC5B;QAED,IAAI,uBAAA,IAAI,wBAAQ,EAAE;YAChB,IAAI;gBACF,MAAM,uBAAA,IAAI,wBAAQ,CAAC,cAAc,CAAC,KAAK,EAAC,MAAM,EAAC,EAAE;oBAC/C,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBACtB,CAAC,CAAC,CAAC;gBACH,IAAI,uBAAA,IAAI,wBAAQ,EAAE;oBAChB,MAAM,uBAAA,IAAI,wBAAQ,CAAC,OAAO,EAAE,CAAC;oBAC7B,uBAAA,IAAI,oBAAW,SAAS,MAAA,CAAC;iBAC1B;aACF;YAAC,MAAM;gBACN,oEAAoE;aACrE;SACF;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,KAAc;QACxB,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,oFAAoF;YACpF,0EAA0E;YAC1E,gCAAgC;YAChC,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CACpB,sDAAsD,CACvD,EACD;gBACA,OAAO,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;aACpD;YAED,uDAAuD;YACvD,kDAAkD;YAClD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;gBAC7D,OAAO;aACR;YAED,iEAAiE;YACjE,aAAa;YACb,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC,EAAE;gBACnE,OAAO;aACR;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA7LD,4BA6LC;;AAED;;GAEG;AACH,MAAa,WAAW;IAAxB;QACE,6BAAwB,IAAI,GAAG,EAAY,EAAC;IAwB9C,CAAC;IAtBC,GAAG,CAAC,IAAmB;QACrB,uBAAA,IAAI,0BAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,IAAmB;QACxB,uBAAA,IAAI,0BAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,YAAY,CAAC,KAAa;QACxB,KAAK,MAAM,IAAI,IAAI,uBAAA,IAAI,0BAAO,EAAE;YAC9B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACvB;QACD,uBAAA,IAAI,0BAAO,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,MAAM,OAAO,CAAC,GAAG,CACf,CAAC,GAAG,uBAAA,IAAI,0BAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF;AAzBD,kCAyBC"}