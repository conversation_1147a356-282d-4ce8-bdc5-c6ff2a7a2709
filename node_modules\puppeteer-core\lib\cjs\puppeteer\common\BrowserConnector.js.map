{"version": 3, "file": "BrowserConnector.js", "sourceRoot": "", "sources": ["../../../../src/common/BrowserConnector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAqC;AACrC,uDAAiD;AACjD,sDAAyC;AACzC,iDAAyC;AAEzC,6CAAwC;AACxC,mDAA2C;AAE3C,yCAAoC;AAqCpC,MAAM,0BAA0B,GAAG,KAAK,IAAI,EAAE;IAC5C,OAAO,uBAAM;QACX,CAAC,CAAC,CAAC,wDAAa,6BAA6B,GAAC,CAAC,CAAC,sBAAsB;QACtE,CAAC,CAAC,CAAC,wDAAa,gCAAgC,GAAC,CAAC;aAC7C,yBAAyB,CAAC;AACnC,CAAC,CAAC;AAEF;;;;;GAKG;AACI,KAAK,UAAU,oBAAoB,CACxC,OAIC;IAED,MAAM,EACJ,iBAAiB,EACjB,UAAU,EACV,iBAAiB,GAAG,KAAK,EACzB,eAAe,GAAG,EAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,EAC3C,SAAS,EACT,MAAM,GAAG,CAAC,EACV,YAAY,EACZ,aAAa,EAAE,YAAY,GAC5B,GAAG,OAAO,CAAC;IAEZ,IAAA,kBAAM,EACJ,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QACtE,CAAC,EACH,+FAA+F,CAChG,CAAC;IAEF,IAAI,UAAuB,CAAC;IAC5B,IAAI,SAAS,EAAE;QACb,UAAU,GAAG,IAAI,0BAAU,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;KACpD;SAAM,IAAI,iBAAiB,EAAE;QAC5B,MAAM,cAAc,GAAG,MAAM,0BAA0B,EAAE,CAAC;QAC1D,MAAM,mBAAmB,GACvB,MAAM,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACjD,UAAU,GAAG,IAAI,0BAAU,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;KAC7E;SAAM,IAAI,UAAU,EAAE;QACrB,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC,CAAC;QACtD,MAAM,cAAc,GAAG,MAAM,0BAA0B,EAAE,CAAC;QAC1D,MAAM,mBAAmB,GACvB,MAAM,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC7C,UAAU,GAAG,IAAI,0BAAU,CAAC,aAAa,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;KACzE;IACD,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAE5D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC/D,CAAC,CAAC,SAAS;QACX,CAAC,CAAC,QAAQ,CAAC;IAEb,MAAM,EAAC,iBAAiB,EAAC,GAAG,MAAM,UAAU,CAAC,IAAI,CAC/C,2BAA2B,CAC5B,CAAC;IACF,MAAM,OAAO,GAAG,MAAM,uBAAU,CAAC,OAAO,CACtC,OAAO,IAAI,QAAQ,EACnB,UAAU,EACV,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EACf,SAAS,EACT,GAAG,EAAE;QACH,OAAO,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;IAC5D,CAAC,EACD,YAAY,EACZ,YAAY,CACb,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC;AA9DD,oDA8DC;AAED,KAAK,UAAU,aAAa,CAAC,UAAkB;IAC7C,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;IAEzD,MAAM,KAAK,GAAG,MAAM,IAAA,mBAAQ,GAAE,CAAC;IAC/B,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE;YACjD,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,QAAQ,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;SAC9C;QACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,oBAAoB,CAAC;KAClC;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,EAAE;YACtB,KAAK,CAAC,OAAO;gBACX,8CAA8C,WAAW,IAAI;oBAC7D,KAAK,CAAC,OAAO,CAAC;SACjB;QACD,MAAM,KAAK,CAAC;KACb;AACH,CAAC"}