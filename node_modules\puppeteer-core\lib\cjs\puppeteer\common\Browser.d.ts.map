{"version": 3, "file": "Browser.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/Browser.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAG3C,OAAO,EAAa,UAAU,EAA0B,MAAM,iBAAiB,CAAC;AAEhF,OAAO,EAAC,IAAI,EAAC,MAAM,gBAAgB,CAAC;AACpC,OAAO,EAAC,QAAQ,EAAC,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAC,MAAM,EAAC,MAAM,aAAa,CAAC;AAEnC,OAAO,EAAC,aAAa,EAA6B,MAAM,oBAAoB,CAAC;AAG7E,OAAO,EACL,OAAO,IAAI,WAAW,EACtB,cAAc,EACd,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EAGpB,qBAAqB,EAErB,oBAAoB,EACpB,UAAU,EACX,MAAM,mBAAmB,CAAC;AAE3B;;GAEG;AACH,qBAAa,UAAW,SAAQ,WAAW;;IACzC;;OAEG;WACU,OAAO,CAClB,OAAO,EAAE,SAAS,GAAG,QAAQ,GAAG,SAAS,EACzC,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,MAAM,EAAE,EACpB,iBAAiB,EAAE,OAAO,EAC1B,eAAe,CAAC,EAAE,QAAQ,GAAG,IAAI,EACjC,OAAO,CAAC,EAAE,YAAY,EACtB,aAAa,CAAC,EAAE,oBAAoB,EACpC,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,oBAAoB,CAAC,EAAE,oBAAoB,GAC1C,OAAO,CAAC,UAAU,CAAC;IA2BtB;;OAEG;IACH,IAAa,QAAQ,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAE3C;IAED;;OAEG;gBAED,OAAO,EAAE,QAAQ,GAAG,SAAS,GAAG,SAAS,EACzC,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,MAAM,EAAE,EACpB,iBAAiB,EAAE,OAAO,EAC1B,eAAe,CAAC,EAAE,QAAQ,GAAG,IAAI,EACjC,OAAO,CAAC,EAAE,YAAY,EACtB,aAAa,CAAC,EAAE,oBAAoB,EACpC,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,oBAAoB,CAAC,EAAE,oBAAoB;IA2C7C;;OAEG;IACY,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAwBvC;;OAEG;IACM,OAAO,IAAI,IAAI;IAuBxB;;;OAGG;IACM,OAAO,IAAI,YAAY,GAAG,IAAI;IAIvC;;OAEG;IACH,cAAc,IAAI,aAAa;IAgB/B;;OAEG;IACM,wBAAwB,IAAI,oBAAoB,GAAG,SAAS;IAIrE;;;;;;;;;;;;;;;;;OAiBG;IACY,6BAA6B,CAC1C,OAAO,GAAE,qBAA0B,GAClC,OAAO,CAAC,iBAAiB,CAAC;IAmB7B;;;OAGG;IACM,eAAe,IAAI,iBAAiB,EAAE;IAI/C;;OAEG;IACM,qBAAqB,IAAI,iBAAiB;IAInD;;OAEG;IACY,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAoFjE;;;;;;;;;;;;;;;;OAgBG;IACM,UAAU,IAAI,MAAM;IAI7B;;;OAGG;IACY,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIvC;;OAEG;IACY,oBAAoB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAsBtE;;;OAGG;IACM,OAAO,IAAI,MAAM,EAAE;IAQ5B;;OAEG;IACM,MAAM,IAAI,MAAM;IAUzB;;;;;;;;;;;;;;;;OAgBG;IACY,aAAa,CAC1B,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,EACpD,OAAO,GAAE,oBAAyB,GACjC,OAAO,CAAC,MAAM,CAAC;IA4BlB;;;;;;;;OAQG;IACY,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;IAYvC;;;;;;;;;OASG;IACY,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAKzC;;;OAGG;IACY,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IAK3C;;;;OAIG;IACY,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAKrC;;;;OAIG;IACM,UAAU,IAAI,IAAI;IAK3B;;OAEG;IACM,WAAW,IAAI,OAAO;CAOhC;AAED;;GAEG;AACH,qBAAa,iBAAkB,SAAQ,cAAc;;IAKnD;;OAEG;gBACS,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,EAAE,MAAM;IAO3E;;OAEG;IACM,OAAO,IAAI,MAAM,EAAE;IAM5B;;;;;;;;;;;;;;;;;;;OAmBG;IACM,aAAa,CACpB,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,EACpD,OAAO,GAAE;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAM,GAC/B,OAAO,CAAC,MAAM,CAAC;IAMlB;;;;;;OAMG;IACY,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;IAqBvC;;;;;;OAMG;IACM,WAAW,IAAI,OAAO;IAI/B;;;;;;;;;;;;;OAaG;IACY,mBAAmB,CAChC,MAAM,EAAE,MAAM,EACd,WAAW,EAAE,UAAU,EAAE,GACxB,OAAO,CAAC,IAAI,CAAC;IAgBhB;;;;;;;;;;;OAWG;IACY,wBAAwB,IAAI,OAAO,CAAC,IAAI,CAAC;IAMxD;;OAEG;IACM,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIjC;;OAEG;IACM,OAAO,IAAI,UAAU;IAI9B;;;;;;OAMG;IACY,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAItC"}