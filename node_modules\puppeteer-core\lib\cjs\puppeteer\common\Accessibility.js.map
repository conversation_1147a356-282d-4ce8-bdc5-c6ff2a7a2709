{"version": 3, "file": "Accessibility.js", "sourceRoot": "", "sources": ["../../../../src/common/Accessibility.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AA+FH;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,aAAa;IAGxB;;OAEG;IACH,YAAY,MAAkB;QAL9B,wCAAoB;QAMlB,uBAAA,IAAI,yBAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsCG;IACI,KAAK,CAAC,QAAQ,CACnB,UAA2B,EAAE;;QAE7B,MAAM,EAAC,eAAe,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAC,GAAG,OAAO,CAAC;QACtD,MAAM,EAAC,KAAK,EAAC,GAAG,MAAM,uBAAA,IAAI,6BAAQ,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACvE,IAAI,aAAiC,CAAC;QACtC,IAAI,IAAI,EAAE;YACR,MAAM,EAAC,IAAI,EAAC,GAAG,MAAM,uBAAA,IAAI,6BAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACzD,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,QAAQ;aACvC,CAAC,CAAC;YACH,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SACpC;QACD,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,MAAM,GAAkB,WAAW,CAAC;QACxC,IAAI,aAAa,EAAE;YACjB,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,KAAK,aAAa,CAAC;YACzD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,IAAI,CAAC;aACb;SACF;QACD,IAAI,CAAC,eAAe,EAAE;YACpB,OAAO,MAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mCAAI,IAAI,CAAC;SAC9C;QAED,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;QAC3C,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;QACnE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACjC,OAAO,IAAI,CAAC;SACb;QACD,OAAO,MAAA,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,mCAAI,IAAI,CAAC;IACjE,CAAC;IAEO,aAAa,CACnB,IAAY,EACZ,gBAA8B;QAE9B,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC;SAC/D;QAED,IAAI,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACnD,OAAO,QAAQ,CAAC;SACjB;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC;SACpC;QACD,OAAO,CAAC,cAAc,CAAC,CAAC;IAC1B,CAAC;IAEO,uBAAuB,CAC7B,UAAuB,EACvB,IAAY,EACZ,aAAsB;QAEtB,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;YACrC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACtB;QACD,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,OAAO;SACR;QACD,aAAa,GAAG,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QAClD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjC,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;SAChE;IACH,CAAC;CACF;AAvHD,sCAuHC;;AAED,MAAM,MAAM;IAaV,YAAY,OAAsC;;QAX3C,aAAQ,GAAa,EAAE,CAAC;QAE/B,iCAAkB,KAAK,EAAC;QACxB,2BAAY,KAAK,EAAC;QAClB,4BAAa,KAAK,EAAC;QACnB,yBAAU,KAAK,EAAC;QAChB,+BAAc;QACd,+BAAc;QACd,kCAAkB;QAClB,kDAAmC;QAGjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,uBAAA,IAAI,gBAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAA,CAAC;QAC9D,uBAAA,IAAI,gBAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,MAAA,CAAC;QACrE,uBAAA,IAAI,mBAAY,IAAI,CAAC,OAAO,CAAC,OAAO,MAAA,CAAC;QAErC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE;YACpD,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE;gBAChC,uBAAA,IAAI,0BAAmB,QAAQ,CAAC,KAAK,CAAC,KAAK,KAAK,UAAU,MAAA,CAAC;gBAC3D,uBAAA,IAAI,oBAAa,IAAI,MAAA,CAAC;aACvB;YACD,IAAI,QAAQ,CAAC,IAAI,KAAK,WAAW,EAAE;gBACjC,uBAAA,IAAI,qBAAc,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAA,CAAC;aACxC;YACD,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC9B,uBAAA,IAAI,kBAAW,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAA,CAAC;aACrC;SACF;IACH,CAAC;IA8BM,IAAI,CAAC,SAAiC;QAC3C,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;YACnB,OAAO,IAAI,CAAC;SACb;QACD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,IAAI,MAAM,EAAE;gBACV,OAAO,MAAM,CAAC;aACf;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QAED,mEAAmE;QACnE,2EAA2E;QAC3E,2EAA2E;QAC3E,gBAAgB;QAChB,IAAI,uBAAA,IAAI,mDAAkB,MAAtB,IAAI,CAAoB,IAAI,uBAAA,IAAI,mDAAkB,MAAtB,IAAI,CAAoB,EAAE;YACxD,OAAO,IAAI,CAAC;SACb;QAED,yEAAyE;QACzE,oDAAoD;QACpD,8EAA8E;QAC9E,wCAAwC;QACxC,QAAQ,uBAAA,IAAI,oBAAM,EAAE;YAClB,KAAK,WAAW,CAAC;YACjB,KAAK,iBAAiB,CAAC;YACvB,KAAK,KAAK,CAAC;YACX,KAAK,OAAO,CAAC;YACb,KAAK,WAAW,CAAC;YACjB,KAAK,QAAQ,CAAC;YACd,KAAK,WAAW,CAAC;YACjB,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC;YACd;gBACE,MAAM;SACT;QAED,qCAAqC;QACrC,IAAI,uBAAA,IAAI,oDAAmB,MAAvB,IAAI,CAAqB,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;QACD,IAAI,uBAAA,IAAI,yBAAW,IAAI,uBAAA,IAAI,oBAAM,EAAE;YACjC,OAAO,IAAI,CAAC;SACb;QACD,IAAI,uBAAA,IAAI,oBAAM,KAAK,SAAS,IAAI,uBAAA,IAAI,oBAAM,EAAE;YAC1C,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,SAAS;QACd,QAAQ,uBAAA,IAAI,oBAAM,EAAE;YAClB,KAAK,QAAQ,CAAC;YACd,KAAK,UAAU,CAAC;YAChB,KAAK,WAAW,CAAC;YACjB,KAAK,UAAU,CAAC;YAChB,KAAK,oBAAoB,CAAC;YAC1B,KAAK,SAAS,CAAC;YACf,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC;YAChB,KAAK,kBAAkB,CAAC;YACxB,KAAK,eAAe,CAAC;YACrB,KAAK,OAAO,CAAC;YACb,KAAK,WAAW,CAAC;YACjB,KAAK,WAAW,CAAC;YACjB,KAAK,QAAQ,CAAC;YACd,KAAK,YAAY,CAAC;YAClB,KAAK,QAAQ,CAAC;YACd,KAAK,KAAK,CAAC;YACX,KAAK,SAAS,CAAC;YACf,KAAK,MAAM,CAAC;YACZ,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC;IAEM,aAAa,CAAC,aAAsB;QACzC,MAAM,IAAI,GAAG,uBAAA,IAAI,oBAAM,CAAC;QACxB,IAAI,IAAI,KAAK,SAAS,IAAI,uBAAA,IAAI,sBAAQ,IAAI,uBAAA,IAAI,uBAAS,EAAE;YACvD,OAAO,KAAK,CAAC;SACd;QAED,IAAI,uBAAA,IAAI,yBAAW,IAAI,uBAAA,IAAI,8BAAgB,EAAE;YAC3C,OAAO,IAAI,CAAC;SACb;QAED,uEAAuE;QACvE,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YACpB,OAAO,IAAI,CAAC;SACb;QAED,wDAAwD;QACxD,IAAI,aAAa,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,uBAAA,IAAI,oBAAM,CAAC;IAC3C,CAAC;IAEM,SAAS;QACd,MAAM,UAAU,GAAG,IAAI,GAAG,EAAqC,CAAC;QAChE,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE;YACpD,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACnE;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACrB,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjD;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACnD;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC/D;QAED,MAAM,IAAI,GAAqB;YAC7B,IAAI,EAAE,uBAAA,IAAI,oBAAM;SACjB,CAAC;QAUF,MAAM,oBAAoB,GAAyB;YACjD,MAAM;YACN,OAAO;YACP,aAAa;YACb,cAAc;YACd,iBAAiB;YACjB,WAAW;SACZ,CAAC;QACF,MAAM,0BAA0B,GAAG,CAAC,GAAuB,EAAU,EAAE;YACrE,OAAO,UAAU,CAAC,GAAG,CAAC,GAAG,CAAW,CAAC;QACvC,CAAC,CAAC;QAEF,KAAK,MAAM,kBAAkB,IAAI,oBAAoB,EAAE;YACrD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE;gBACvC,SAAS;aACV;YAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;SAC3E;QAYD,MAAM,iBAAiB,GAAsB;YAC3C,UAAU;YACV,UAAU;YACV,SAAS;YACT,OAAO;YACP,WAAW;YACX,iBAAiB;YACjB,UAAU;YACV,UAAU;YACV,UAAU;SACX,CAAC;QACF,MAAM,uBAAuB,GAAG,CAAC,GAAoB,EAAW,EAAE;YAChE,OAAO,UAAU,CAAC,GAAG,CAAC,GAAG,CAAY,CAAC;QACxC,CAAC,CAAC;QAEF,KAAK,MAAM,eAAe,IAAI,iBAAiB,EAAE;YAC/C,8EAA8E;YAC9E,wEAAwE;YACxE,QAAQ;YACR,IAAI,eAAe,KAAK,SAAS,IAAI,uBAAA,IAAI,oBAAM,KAAK,aAAa,EAAE;gBACjE,SAAS;aACV;YACD,MAAM,KAAK,GAAG,uBAAuB,CAAC,eAAe,CAAC,CAAC;YACvD,IAAI,CAAC,KAAK,EAAE;gBACV,SAAS;aACV;YACD,IAAI,CAAC,eAAe,CAAC,GAAG,uBAAuB,CAAC,eAAe,CAAC,CAAC;SAClE;QAGD,MAAM,kBAAkB,GAAuB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACtE,KAAK,MAAM,gBAAgB,IAAI,kBAAkB,EAAE;YACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;gBACrC,SAAS;aACV;YACD,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC/C,IAAI,CAAC,gBAAgB,CAAC;gBACpB,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;SACjE;QAGD,MAAM,mBAAmB,GAAyB;YAChD,OAAO;YACP,UAAU;YACV,UAAU;SACX,CAAC;QACF,MAAM,yBAAyB,GAAG,CAAC,GAAuB,EAAU,EAAE;YACpE,OAAO,UAAU,CAAC,GAAG,CAAC,GAAG,CAAW,CAAC;QACvC,CAAC,CAAC;QACF,KAAK,MAAM,iBAAiB,IAAI,mBAAmB,EAAE;YACnD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;gBACtC,SAAS;aACV;YACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;SACxE;QAOD,MAAM,eAAe,GAAoB;YACvC,cAAc;YACd,UAAU;YACV,SAAS;YACT,aAAa;SACd,CAAC;QACF,MAAM,qBAAqB,GAAG,CAAC,GAAkB,EAAU,EAAE;YAC3D,OAAO,UAAU,CAAC,GAAG,CAAC,GAAG,CAAW,CAAC;QACvC,CAAC,CAAC;QACF,KAAK,MAAM,aAAa,IAAI,eAAe,EAAE;YAC3C,MAAM,KAAK,GAAG,qBAAqB,CAAC,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,OAAO,EAAE;gBAC/B,SAAS;aACV;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,qBAAqB,CAAC,aAAa,CAAC,CAAC;SAC5D;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,QAAyC;QAChE,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC3C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC9B,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;SACnD;QACD,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE;YACpC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,EAAE;gBACjD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,CAAC;aAC5C;SACF;QACD,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;IACxC,CAAC;CACF;;IA7RG,IAAI,uBAAA,IAAI,8BAAgB,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IACD,IAAI,uBAAA,IAAI,wBAAU,EAAE;QAClB,OAAO,IAAI,CAAC;KACb;IACD,OAAO,uBAAA,IAAI,oBAAM,KAAK,SAAS,IAAI,uBAAA,IAAI,oBAAM,KAAK,WAAW,CAAC;AAChE,CAAC;IAGC,MAAM,IAAI,GAAG,uBAAA,IAAI,oBAAM,CAAC;IACxB,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,eAAe,CAAC;AAC7E,CAAC;IAGC,IAAI,uBAAA,IAAI,uCAAyB,KAAK,SAAS,EAAE;QAC/C,uBAAA,IAAI,mCAA4B,KAAK,MAAA,CAAC;QACtC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjC,IAAI,uBAAA,KAAK,yBAAW,IAAI,uBAAA,KAAK,oDAAmB,MAAxB,KAAK,CAAqB,EAAE;gBAClD,uBAAA,IAAI,mCAA4B,IAAI,MAAA,CAAC;gBACrC,MAAM;aACP;SACF;KACF;IACD,OAAO,uBAAA,IAAI,uCAAyB,CAAC;AACvC,CAAC"}