{"version": 3, "file": "QueryHandler.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/QueryHandler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,aAAa,MAAM,yBAAyB,CAAC;AAEpD,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACjC,OAAO,EAGL,sBAAsB,EACvB,MAAM,oBAAoB,CAAC;AAE5B;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI,CAAC;IACzD;;OAEG;IACH,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,IAAI,EAAE,CAAC;CACrD;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,QAAQ,CAAC,EAAE,CACT,IAAI,EAAE,IAAI,EACV,QAAQ,EAAE,MAAM,EAChB,aAAa,EAAE,aAAa,KACzB,IAAI,GAAG,IAAI,CAAC;IACjB;;OAEG;IACH,QAAQ,CAAC,EAAE,CACT,IAAI,EAAE,IAAI,EACV,QAAQ,EAAE,MAAM,EAChB,aAAa,EAAE,aAAa,KACzB,IAAI,EAAE,CAAC;CACb;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC;;;;OAIG;IACH,QAAQ,CAAC,EAAE,CACT,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC,EAC5B,QAAQ,EAAE,MAAM,KACb,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACzC;;;;OAIG;IACH,QAAQ,CAAC,EAAE,CACT,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC,EAC5B,QAAQ,EAAE,MAAM,KACb,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAEzC;;;OAGG;IACH,OAAO,CAAC,EAAE,CACR,cAAc,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,EAC3C,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,sBAAsB,KAC5B,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;CAC1C;AAgJD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,wBAAgB,0BAA0B,CACxC,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,kBAAkB,GAC1B,IAAI,CAcN;AAED;;;;GAIG;AACH,wBAAgB,4BAA4B,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAE/D;AAED;;;;GAIG;AACH,wBAAgB,uBAAuB,IAAI,MAAM,EAAE,CAElD;AAED;;;;GAIG;AACH,wBAAgB,wBAAwB,IAAI,IAAI,CAE/C;AAID;;GAEG;AACH,wBAAgB,0BAA0B,CAAC,QAAQ,EAAE,MAAM,GAAG;IAC5D,eAAe,EAAE,MAAM,CAAC;IACxB,YAAY,EAAE,qBAAqB,CAAC;CACrC,CAmBA"}