{"version": 3, "file": "HTTPRequest.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/HTTPRequest.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAC,eAAe,EAAC,MAAM,6CAA6C,CAAC;AAG5E,OAAO,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AAEjC,OAAO,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAE/C;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CAClC;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC,MAAM,EAAE,yBAAyB,CAAC;IAClC,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;;;GAIG;AACH,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACjC,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;CACvB;AAED;;;;GAIG;AACH,oBAAY,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEpE;;;;GAIG;AACH,eAAO,MAAM,qCAAqC,IAAI,CAAC;AAEvD,UAAU,UAAW,SAAQ,YAAY;IACvC,IAAI,CAAC,CAAC,SAAS,MAAM,eAAe,CAAC,QAAQ,EAC3C,MAAM,EAAE,CAAC,EACT,GAAG,SAAS,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GACtD,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;CACvD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,qBAAa,WAAW;;IACtB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;IACpC;;OAEG;IACH,YAAY,EAAE,MAAM,GAAG,IAAI,CAAQ;IACnC;;OAEG;IACH,SAAS,EAAE,YAAY,GAAG,IAAI,CAAQ;IACtC;;OAEG;IACH,gBAAgB,UAAS;IACzB;;OAEG;IACH,cAAc,EAAE,WAAW,EAAE,CAAC;IAsB9B;;;;OAIG;IACH,IAAI,MAAM,IAAI,UAAU,CAEvB;IAED;;OAEG;gBAED,MAAM,EAAE,UAAU,EAClB,KAAK,EAAE,KAAK,GAAG,IAAI,EACnB,cAAc,EAAE,MAAM,GAAG,SAAS,EAClC,iBAAiB,EAAE,OAAO,EAC1B,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAC9C,aAAa,EAAE,WAAW,EAAE;IAuB9B;;OAEG;IACH,GAAG,IAAI,MAAM;IAIb;;;;OAIG;IACH,wBAAwB,IAAI,wBAAwB;IAKpD;;;OAGG;IACH,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI;IAKxD;;OAEG;IACH,gBAAgB,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI;IAKvD;;;;;;;;;;OAUG;IACH,wBAAwB,IAAI,wBAAwB;IAUpD;;;OAGG;IACH,4BAA4B,IAAI,OAAO;IAIvC;;;;;OAKG;IACH,sBAAsB,CACpB,cAAc,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,GAChD,IAAI;IAIP;;;OAGG;IACG,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;IAkB5C;;;OAGG;IACH,YAAY,IAAI,YAAY;IAI5B;;OAEG;IACH,MAAM,IAAI,MAAM;IAIhB;;OAEG;IACH,QAAQ,IAAI,MAAM,GAAG,SAAS;IAI9B;;;OAGG;IACH,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAIjC;;;OAGG;IACH,QAAQ,IAAI,YAAY,GAAG,IAAI;IAI/B;;;OAGG;IACH,KAAK,IAAI,KAAK,GAAG,IAAI;IAIrB;;OAEG;IACH,mBAAmB,IAAI,OAAO;IAI9B;;OAEG;IACH,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS;IAIvC;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,aAAa,IAAI,WAAW,EAAE;IAI9B;;;;;;;;;;;;;;;;;;;OAmBG;IACH,OAAO,IAAI;QAAC,SAAS,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI;IASrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACG,QAAQ,CACZ,SAAS,GAAE,wBAA6B,EACxC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IA6DhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACG,OAAO,CACX,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,EACrC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IA8EhB;;;;;;;;;;;;OAYG;IACG,KAAK,CACT,SAAS,GAAE,SAAoB,EAC/B,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;CAyCjB;AAED;;GAEG;AACH,oBAAY,yBAAyB;IACnC,KAAK,UAAU;IACf,OAAO,YAAY;IACnB,QAAQ,aAAa;IACrB,QAAQ,aAAa;IACrB,IAAI,SAAS;IACb,cAAc,oBAAoB;CACnC;AAED;;;;GAIG;AACH,oBAAY,2BAA2B,GAAG,yBAAyB,CAAC;AAEpE;;GAEG;AACH,oBAAY,SAAS,GACjB,SAAS,GACT,cAAc,GACd,oBAAoB,GACpB,iBAAiB,GACjB,mBAAmB,GACnB,mBAAmB,GACnB,kBAAkB,GAClB,kBAAkB,GAClB,mBAAmB,GACnB,iBAAiB,GACjB,sBAAsB,GACtB,iBAAiB,GACjB,UAAU,GACV,QAAQ,CAAC;AAmBb;;GAEG;AACH,oBAAY,YAAY,GAAG,UAAU,GAAG,OAAO,GAAG,SAAS,CAAC"}